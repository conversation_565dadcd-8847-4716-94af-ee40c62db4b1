from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from sqlmodel import Session, select

from app.core.database import create_db_and_tables, engine
from app.models.models import Agent, Workflow
from app.api import agents, workflows, runs


def seed_database():
    """
    如果数据库中不存在默认智能体和工作流，则进行初始化种子数据。
    """
    with Session(engine) as session:
        # 检查默认智能体是否已存在
        statement = select(Agent).where(Agent.name == "简单研究员")
        existing_agent = session.exec(statement).first()

        if not existing_agent:
            print("正在初始化数据库，创建默认智能体和工作流...")
            # 1. 创建默认智能体
            researcher_agent = Agent(
                name="简单研究员",
                description="一个使用大语言模型进行规划、网络搜索和信息综合的智能体，用于回答问题。",
                model_provider="ollama",
                config={
                    "system_prompt": (
                        "你是一个世界级的研究助手。"
                        "你的目标是基于你制定的计划和网络搜索结果来回答用户的问题。"
                        "1. **规划者**: 首先，创建一个逐步计划来回答问题。你唯一的工具是网络搜索。你的计划应该只是一个搜索查询。"
                        "2. **搜索者**: 网络搜索工具将使用你的查询进行调用。"
                        "3. **综合者**: 最后，使用搜索结果，为原始问题提供全面的答案。"
                    )
                },
            )
            session.add(researcher_agent)
            session.commit()
            session.refresh(researcher_agent)

            # 2. 定义增强的工作流图
            researcher_workflow_graph = {
                "nodes": [
                    {
                        "id": "planner",
                        "type": "AgentNode",
                        "config": {
                            "system_prompt": "你是一个研究规划专家。基于用户的问题：'{question}'，创建一个最优的搜索查询来找到全面的信息。考虑多个方面和关键词。只回复搜索查询。"
                        },
                    },
                    {
                        "id": "searcher",
                        "type": "ToolNode",
                        "config": {"tool_name": "search_web"},
                    },
                    {
                        "id": "analyzer",
                        "type": "AgentNode",
                        "config": {
                            "system_prompt": "你是一个内容分析师。分析搜索结果并确定它们是否充分回答了问题：'{question}'。如果结果不足，建议一个精炼的搜索查询。如果足够，继续进行综合。"
                        },
                    },
                    {
                        "id": "synthesizer",
                        "type": "AgentNode",
                        "config": {
                            "system_prompt": "你是一个研究综合者。基于原始问题：'{question}'和搜索结果，创建一个全面、结构良好的答案。包括关键见解、相关细节和实际意义。"
                        },
                    },
                    {
                        "id": "reporter",
                        "type": "ToolNode",
                        "config": {"tool_name": "generate_report"},
                    },
                ],
                "edges": [
                    {"source": "planner", "target": "searcher"},
                    {"source": "searcher", "target": "analyzer"},
                    {"source": "analyzer", "target": "synthesizer"},
                    {"source": "synthesizer", "target": "reporter"},
                ],
                "entry_point": "planner",
            }

            # 3. 创建默认工作流
            researcher_workflow = Workflow(
                agent_id=researcher_agent.id,
                graph=researcher_workflow_graph,
                status="published",
            )
            session.add(researcher_workflow)
            session.commit()
            print("数据库初始化完成。")


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时运行的代码
    print("正在启动...")
    create_db_and_tables()
    print("数据库表已创建。")
    seed_database()
    yield
    # 关闭时运行的代码
    print("正在关闭...")


app = FastAPI(
    title="智能体工作流框架",
    description="一个用于构建和管理智能体工作流的框架。",
    version="0.1.0 (原型版本)",
    lifespan=lifespan,
)

# CORS中间件配置
origins = [
    "http://localhost:5173",
    "http://localhost:5174",
    "http://localhost:5175",
    "http://localhost:3000",  # React开发服务器的常用端口
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", tags=["健康检查"])
def read_root():
    """
    根端点，用于检查API是否正在运行。
    """
    return {"status": "ok", "message": "欢迎使用智能体工作流框架API！"}


# 包含来自app.api模块的路由器

app.include_router(agents.router, prefix="/api")
app.include_router(workflows.router, prefix="/api")
app.include_router(runs.router, prefix="/api")
