from datetime import datetime
import json
import time
import random


def get_current_time() -> str:
    """
    Returns the current date and time as a formatted string.
    This serves as a simple example of a tool that can be called by an agent.
    """
    return datetime.now().isoformat()


def search_web(query: str) -> str:
    """
    Enhanced mock web search tool that returns realistic search results for different queries.
    """
    print(f"🔍 Searching web for: {query}")

    # Simulate search delay
    time.sleep(1)

    # Mock search results based on query content
    search_results = {
        "langgraph": {
            "title": "LangGraph - Build Multi-Agent Applications",
            "content": "LangGraph is a library for building stateful, multi-actor applications with LLMs. It extends the LangChain expression language with the ability to coordinate multiple chains (or actors) across multiple steps of computation in a cyclic manner. It is inspired by Pregel and Apache Beam. Key features include: state management, conditional routing, human-in-the-loop workflows, and streaming support.",
            "url": "https://langchain-ai.github.io/langgraph/",
            "relevance": 0.95,
        },
        "ai agents": {
            "title": "AI Agents: The Future of Autonomous Systems",
            "content": "AI agents are autonomous systems that can perceive their environment, make decisions, and take actions to achieve specific goals. They combine large language models with tool-calling capabilities, memory systems, and planning algorithms. Modern AI agents can handle complex multi-step tasks, collaborate with other agents, and adapt to changing conditions.",
            "url": "https://example.com/ai-agents",
            "relevance": 0.92,
        },
        "workflow automation": {
            "title": "Workflow Automation with AI",
            "content": "Workflow automation using AI involves creating intelligent systems that can execute complex business processes with minimal human intervention. These systems can handle decision-making, data processing, communication, and integration with various tools and services. Benefits include increased efficiency, reduced errors, and 24/7 operation capability.",
            "url": "https://example.com/workflow-automation",
            "relevance": 0.88,
        },
    }

    # Find the most relevant result
    query_lower = query.lower()
    best_match = None
    best_score = 0

    for key, result in search_results.items():
        if key in query_lower:
            score = result["relevance"]
            if score > best_score:
                best_score = score
                best_match = result

    # If no specific match, return a general result
    if not best_match:
        best_match = {
            "title": f"Search Results for: {query}",
            "content": f"Found relevant information about {query}. This topic involves various aspects of technology, implementation strategies, and best practices. For detailed information, consider consulting specialized resources and documentation.",
            "url": "https://example.com/search",
            "relevance": 0.7,
        }

    result_text = f"Title: {best_match['title']}\nContent: {best_match['content']}\nSource: {best_match['url']}\nRelevance: {best_match['relevance']:.0%}"
    print(f"✅ Search completed. Relevance: {best_match['relevance']:.0%}")
    return result_text


def analyze_sentiment(text: str) -> str:
    """
    Analyzes the sentiment of the given text.
    """
    print(f"🎭 Analyzing sentiment for text: {text[:50]}...")

    # Simple sentiment analysis based on keywords
    positive_words = [
        "good",
        "great",
        "excellent",
        "amazing",
        "wonderful",
        "fantastic",
        "love",
        "like",
        "happy",
        "positive",
    ]
    negative_words = [
        "bad",
        "terrible",
        "awful",
        "hate",
        "dislike",
        "sad",
        "negative",
        "poor",
        "disappointing",
    ]

    text_lower = text.lower()
    positive_count = sum(1 for word in positive_words if word in text_lower)
    negative_count = sum(1 for word in negative_words if word in text_lower)

    if positive_count > negative_count:
        sentiment = "positive"
        confidence = min(0.9, 0.6 + (positive_count - negative_count) * 0.1)
    elif negative_count > positive_count:
        sentiment = "negative"
        confidence = min(0.9, 0.6 + (negative_count - positive_count) * 0.1)
    else:
        sentiment = "neutral"
        confidence = 0.5

    result = {
        "sentiment": sentiment,
        "confidence": confidence,
        "positive_indicators": positive_count,
        "negative_indicators": negative_count,
    }

    print(f"✅ Sentiment analysis complete: {sentiment} ({confidence:.0%} confidence)")
    return json.dumps(result, indent=2)


def summarize_text(text: str, max_length: int = 100) -> str:
    """
    Creates a summary of the given text.
    """
    print(f"📝 Summarizing text ({len(text)} characters)...")

    # Simple extractive summarization
    sentences = text.split(". ")
    if len(sentences) <= 2:
        summary = text
    else:
        # Take first and last sentences, plus one from the middle
        middle_idx = len(sentences) // 2
        key_sentences = [sentences[0], sentences[middle_idx], sentences[-1]]
        summary = ". ".join(key_sentences)

    # Truncate if too long
    if len(summary) > max_length:
        summary = summary[: max_length - 3] + "..."

    print(f"✅ Summary created ({len(summary)} characters)")
    return summary


def generate_report(data: str) -> str:
    """
    Generates a structured report from the provided data.
    """
    print(f"📊 Generating report from data...")

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    report = f"""
# Research Report
Generated on: {timestamp}

## Executive Summary
This report presents findings based on the collected data and analysis.

## Key Findings
{data}

## Methodology
- Data collection through web search
- Content analysis and summarization
- Structured presentation of results

## Recommendations
Based on the analysis, the following recommendations are suggested:
1. Further investigation into identified topics
2. Implementation of best practices
3. Continuous monitoring and evaluation

## Conclusion
The research provides valuable insights that can inform decision-making and strategy development.

---
Report ID: RPT-{random.randint(1000, 9999)}
"""

    print(f"✅ Report generated successfully")
    return report.strip()


# Tool registry for easy access
AVAILABLE_TOOLS = {
    "get_current_time": get_current_time,
    "search_web": search_web,
    "analyze_sentiment": analyze_sentiment,
    "summarize_text": summarize_text,
    "generate_report": generate_report,
}
