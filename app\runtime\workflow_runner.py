from langgraph.graph import StateGraph, END
from typing import TypedDict, Annotated, List
import operator
from langchain_core.messages import (
    AnyMessage,
    SystemMessage,
    HumanMessage,
    ToolMessage,
    AIMessage,
)

from app.runtime import tools
from app.core.llm import get_llm


# 1. Define the state for our graph
class AgentState(TypedDict):
    """
    Represents the state of our agent.
    """

    messages: Annotated[List[AnyMessage], operator.add]


class WorkflowRunner:
    """
    Compiles and runs a workflow defined in JSON format using LangGraph.
    """

    def __init__(self, workflow_def: dict):
        self.workflow_def = workflow_def
        self.llm = get_llm()
        self.tool_registry = tools.AVAILABLE_TOOLS
        self.graph = self._compile()

    def _agent_node(self, state: AgentState):
        """调用大语言模型的节点。"""
        # 向系统消息添加可用工具信息
        messages = state["messages"].copy()

        # 检查是否需要添加工具信息
        if len(messages) > 0 and isinstance(messages[0], SystemMessage):
            # 更新系统消息以包含工具信息
            original_content = messages[0].content
            tool_info = """

可用工具：
- search_web(query): 搜索网络信息
- get_current_time(): 获取当前日期和时间
- analyze_sentiment(text): 分析文本情感
- summarize_text(text): 总结给定文本
- generate_report(data): 生成结构化报告

要使用工具，请回复：TOOL_CALL: tool_name(arguments)
例如：TOOL_CALL: search_web("LangGraph教程")
"""
            messages[0] = SystemMessage(content=original_content + tool_info)

        response = self.llm.invoke(messages)

        # 检查响应是否包含工具调用
        if "TOOL_CALL:" in response.content:
            # 解析并执行工具调用
            tool_call_line = [
                line for line in response.content.split("\n") if "TOOL_CALL:" in line
            ][0]
            tool_call_part = tool_call_line.split("TOOL_CALL:")[1].strip()

            # 简单解析 - 提取工具名称和参数
            if "(" in tool_call_part and ")" in tool_call_part:
                tool_name = tool_call_part.split("(")[0].strip()
                arg_part = (
                    tool_call_part.split("(")[1].split(")")[0].strip().strip("\"'")
                )

                if tool_name in self.tool_registry:
                    try:
                        if tool_name == "get_current_time":
                            tool_result = self.tool_registry[tool_name]()
                        else:
                            tool_result = self.tool_registry[tool_name](arg_part)

                        # 将工具结果作为新消息添加
                        tool_message = HumanMessage(
                            content=f"工具 {tool_name} 的结果: {tool_result}"
                        )
                        return {"messages": [response, tool_message]}
                    except Exception as e:
                        error_message = HumanMessage(content=f"工具错误: {str(e)}")
                        return {"messages": [response, error_message]}

        return {"messages": [response]}

    def _tool_node(self, state: AgentState):
        """基于最后一条AI消息执行工具的节点。"""
        tool_messages = []
        last_message = state["messages"][-1]

        if not isinstance(last_message, AIMessage) or not last_message.tool_calls:
            # 没有工具调用，所以我们可以在这里结束图形或以不同方式处理
            return {"messages": []}

        for tool_call in last_message.tool_calls:
            tool_name = tool_call["name"]
            if tool_name in self.tool_registry:
                tool_to_call = self.tool_registry[tool_name]
                try:
                    # 处理不同的参数结构
                    args = tool_call["args"]
                    if tool_name == "get_current_time":
                        result = tool_to_call()
                    elif tool_name in [
                        "search_web",
                        "analyze_sentiment",
                        "summarize_text",
                    ]:
                        # 这些工具期望'query'或'text'参数
                        query = (
                            args.get("query")
                            or args.get("text")
                            or args.get("input", "")
                        )
                        result = tool_to_call(query)
                    elif tool_name == "generate_report":
                        # 生成报告期望'data'参数
                        data = (
                            args.get("data")
                            or args.get("query")
                            or args.get("input", "")
                        )
                        result = tool_to_call(data)
                    else:
                        # 后备方案：尝试使用query参数调用
                        query = args.get("query", "")
                        result = tool_to_call(query=query)

                    tool_messages.append(
                        ToolMessage(
                            content=str(result),
                            name=tool_name,
                            tool_call_id=tool_call["id"],
                        )
                    )
                except Exception as e:
                    print(f"执行工具 {tool_name} 时出错: {e}")
                    tool_messages.append(
                        ToolMessage(
                            content=f"执行 {tool_name} 时出错: {str(e)}",
                            name=tool_name,
                            tool_call_id=tool_call["id"],
                        )
                    )

        return {"messages": tool_messages}

    def _router(self, state: AgentState):
        """决定下一步的条件边。"""
        last_message = state["messages"][-1]
        if isinstance(last_message, AIMessage) and last_message.tool_calls:
            return "tools"
        return "end"

    def _compile(self):
        """
        将JSON工作流定义编译为可执行的LangGraph。
        """
        workflow = StateGraph(AgentState)

        # 从工作流定义动态添加节点
        for node_def in self.workflow_def.get("nodes", []):
            node_name = node_def["id"]
            node_type = node_def["type"]
            if node_type == "AgentNode":
                workflow.add_node(node_name, self._agent_node)
            elif node_type == "ToolNode":
                workflow.add_node(node_name, self._tool_node)

        # 设置入口点
        entry_point = self.workflow_def.get("entry_point")
        if entry_point:
            workflow.set_entry_point(entry_point)

        # 添加边
        edges = self.workflow_def.get("edges", [])
        if not edges:
            # 如果没有边，为单节点工作流添加到END的边
            nodes = self.workflow_def.get("nodes", [])
            if len(nodes) == 1:
                workflow.add_edge(nodes[0]["id"], END)
        else:
            for edge_def in edges:
                source = edge_def["source"]
                target = edge_def["target"]

                # 处理条件路由
                if edge_def.get("conditional"):
                    workflow.add_conditional_edges(
                        source, self._router, {"tools": target, "end": END}
                    )
                else:
                    workflow.add_edge(source, target)

        return workflow.compile()

    async def run(self, inputs: dict):
        """
        运行编译后的图形，使用给定的输入。
        """
        initial_messages = [
            SystemMessage(content=inputs.get("system_prompt", "你是一个有用的助手。")),
            HumanMessage(content=inputs.get("question", "你好！")),
        ]
        final_state = None
        # 收集所有输出状态
        all_states = []
        async for output in self.graph.astream({"messages": initial_messages}):
            all_states.append(output)
            final_state = output

        # 确保返回包含所有消息的最终状态
        if final_state and "messages" in final_state:
            return final_state
        else:
            # 如果没有最终状态，创建一个包含所有消息的状态
            all_messages = initial_messages.copy()
            for state in all_states:
                if "messages" in state:
                    all_messages.extend(state["messages"])
            return {"messages": all_messages}
