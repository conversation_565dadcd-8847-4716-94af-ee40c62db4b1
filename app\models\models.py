import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlmodel import Field, JSON, SQLModel, Column, Relationship


def new_uuid() -> str:
    """Generates a new UUID string."""
    return str(uuid.uuid4())


class Agent(SQLModel, table=True):
    """Represents an agent that can execute workflows."""
    id: str = Field(default_factory=new_uuid, primary_key=True)
    name: str = Field(index=True)
    description: Optional[str] = None
    model_provider: str = "ollama"
    config: Dict[str, Any] = Field(default={}, sa_column=Column(JSON))
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)
    updated_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)

    workflows: List["Workflow"] = Relationship(back_populates="agent")


class Workflow(SQLModel, table=True):
    """Represents a graph of nodes that an agent can execute."""
    id: str = Field(default_factory=new_uuid, primary_key=True)
    graph: Dict[str, Any] = Field(default={}, sa_column=Column(JSON))
    version: int = 1
    status: str = "draft"
    created_at: datetime = Field(default_factory=datetime.utcnow, nullable=False)

    agent_id: Optional[str] = Field(default=None, foreign_key="agent.id")
    agent: Optional[Agent] = Relationship(back_populates="workflows")

    runs: List["Run"] = Relationship(back_populates="workflow")


class Run(SQLModel, table=True):
    """Represents a single execution of a workflow."""
    id: str = Field(default_factory=new_uuid, primary_key=True)
    status: Optional[str] = Field(default=None, index=True)
    started_at: Optional[datetime] = None
    finished_at: Optional[datetime] = None
    metrics: Dict[str, Any] = Field(default={}, sa_column=Column(JSON))

    workflow_id: str = Field(foreign_key="workflow.id")
    workflow: Workflow = Relationship(back_populates="runs")


class RunCreate(SQLModel):
    """Request model for triggering a new run."""
    workflow_id: str
    inputs: Dict[str, Any]