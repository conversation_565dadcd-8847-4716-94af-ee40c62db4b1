from langchain_ollama import ChatOllama
from functools import lru_cache

# Ollama配置
# 在实际应用中，这些配置应该来自配置文件或环境变量
OLLAMA_BASE_URL = "http://10.0.0.6:11434"
DEFAULT_MODEL = "qwen3:32b"


@lru_cache
def get_llm(model: str = DEFAULT_MODEL, temperature: float = 0.1):
    """
    返回ChatOllama模型的缓存实例。
    缓存确保我们不会为相同的参数重新初始化模型。
    """
    return ChatOllama(
        base_url=OLLAMA_BASE_URL,
        model=model,
        temperature=temperature,
    )
