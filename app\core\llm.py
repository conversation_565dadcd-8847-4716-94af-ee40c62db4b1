from langchain_ollama import ChatOllama
from functools import lru_cache

# Configuration for Ollama
# In a real app, this would come from a config file or environment variables
OLLAMA_BASE_URL = "http://10.0.0.6:11434"
DEFAULT_MODEL = "qwen2.5:14b"


@lru_cache
def get_llm(model: str = DEFAULT_MODEL, temperature: float = 0.1):
    """
    Returns a cached instance of the ChatOllama model.
    Caching ensures we don't re-initialize the model for the same parameters.
    """
    return ChatOllama(
        base_url=OLLAMA_BASE_URL,
        model=model,
        temperature=temperature,
    )
