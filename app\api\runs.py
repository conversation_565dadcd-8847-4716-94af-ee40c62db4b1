from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlmodel import Session

from app.core.database import engine
from app.models.models import Run, Workflow, RunCreate
from app.runtime.workflow_runner import WorkflowRunner


def get_session():
    with Session(engine) as session:
        yield session


router = APIRouter(
    prefix="/runs",
    tags=["Runs"],
)


async def execute_workflow_in_background(
    run_id: str, workflow_def: dict, inputs: dict, session: Session
):
    """
    A wrapper function to run the workflow and update the run status.
    """
    runner = WorkflowRunner(workflow_def)
    run = session.get(Run, run_id)
    if not run:
        return  # Should not happen

    run.status = "running"
    session.commit()
    session.refresh(run)

    try:
        # Execute the graph
        final_state = await runner.run(inputs)
        run.status = "completed"
        # Serialize the final state messages to be stored in JSON
        if final_state and "messages" in final_state:
            # Convert messages to dict format for JSON storage
            output_messages = []
            for msg in final_state["messages"]:
                if hasattr(msg, "dict"):
                    output_messages.append(msg.dict())
                else:
                    # Fallback for messages that don't have dict method
                    output_messages.append(
                        {
                            "type": type(msg).__name__,
                            "content": str(msg.content)
                            if hasattr(msg, "content")
                            else str(msg),
                        }
                    )
            run.metrics["output"] = output_messages
        else:
            run.metrics["output"] = [
                {"type": "info", "content": "No output messages generated"}
            ]
    except Exception as e:
        run.status = "failed"
        run.metrics["error"] = str(e)
        # Log the error
        print(f"Run {run_id} failed: {e}")
        import traceback

        traceback.print_exc()
    finally:
        session.commit()


@router.get("/{run_id}", response_model=Run)
def get_run(*, run_id: str, session: Session = Depends(get_session)):
    """
    Get the details of a specific run.
    """
    run = session.get(Run, run_id)
    if not run:
        raise HTTPException(status_code=404, detail="Run not found")
    return run


@router.post("/", response_model=Run)
def trigger_run(
    *,
    run_data: RunCreate,
    background_tasks: BackgroundTasks,
    session: Session = Depends(get_session),
):
    """
    Trigger a new run for a given workflow.
    """
    workflow = session.get(Workflow, run_data.workflow_id)
    if not workflow:
        raise HTTPException(status_code=404, detail="Workflow not found")

    # Create a new Run record in the database
    new_run = Run(
        workflow_id=run_data.workflow_id,
        status="pending",
        metrics={"inputs": run_data.inputs},
    )
    session.add(new_run)
    session.commit()
    session.refresh(new_run)

    # Add the long-running task to the background
    background_tasks.add_task(
        execute_workflow_in_background,
        new_run.id,
        workflow.graph,
        run_data.inputs,
        session,
    )

    return new_run
