#!/usr/bin/env python3
"""
Simple Workflow Demo Script

This script demonstrates how to create and run a simple workflow using the NeoAgent framework.
It will create a basic research workflow that uses Ollama to answer questions.
"""

import requests
import json
import time
from typing import Dict, Any

# API基础URL
API_BASE = "http://localhost:8765/api"


def create_simple_agent() -> str:
    """创建一个简单的研究智能体。"""
    agent_data = {
        "name": "演示研究智能体",
        "description": "一个用于演示Ollama工作流能力的简单智能体",
        "model_provider": "ollama",
        "config": {
            "temperature": 0.1,
            "system_prompt": "你是一个有用的研究助手。使用可用的工具来查找信息并提供全面的答案。",
        },
    }

    response = requests.post(f"{API_BASE}/agents", json=agent_data)
    if response.status_code == 200:
        agent = response.json()
        print(f"✅ 已创建智能体: {agent['name']} (ID: {agent['id']})")
        return agent["id"]
    else:
        print(f"❌ 创建智能体失败: {response.text}")
        return None


def create_simple_workflow(agent_id: str) -> str:
    """创建一个演示基本智能体能力的简单工作流。"""
    workflow_data = {
        "agent_id": agent_id,
        "graph": {
            "nodes": [
                {
                    "id": "researcher",
                    "type": "AgentNode",
                    "config": {
                        "system_prompt": "你是一个研究助手。基于用户的问题，搜索相关信息并提供全面的答案。"
                    },
                }
            ],
            "edges": [],
            "entry_point": "researcher",
        },
    }

    response = requests.post(f"{API_BASE}/workflows", json=workflow_data)
    if response.status_code == 200:
        workflow = response.json()
        print(f"✅ Created workflow: {workflow['id']}")
        return workflow["id"]
    else:
        print(f"❌ Failed to create workflow: {response.text}")
        return None


def run_workflow(workflow_id: str, question: str) -> str:
    """Run the workflow with a given question."""
    run_data = {
        "workflow_id": workflow_id,
        "inputs": {
            "question": question,
            "system_prompt": "You are a helpful research assistant. Use the available tools to find information and answer the user's question comprehensively.",
        },
    }

    response = requests.post(f"{API_BASE}/runs", json=run_data)
    if response.status_code == 200:
        run = response.json()
        print(f"✅ Started run: {run['id']}")
        return run["id"]
    else:
        print(f"❌ Failed to start run: {response.text}")
        return None


def check_run_status(run_id: str) -> Dict[str, Any]:
    """Check the status of a run."""
    response = requests.get(f"{API_BASE}/runs/{run_id}")
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Failed to get run status: {response.text}")
        return None


def wait_for_completion(run_id: str, max_wait_time: int = 120) -> Dict[str, Any]:
    """Wait for a run to complete."""
    start_time = time.time()

    while time.time() - start_time < max_wait_time:
        run_status = check_run_status(run_id)
        if run_status:
            status = run_status.get("status")
            print(f"🔄 Run status: {status}")

            if status in ["completed", "failed"]:
                return run_status

        time.sleep(2)

    print(f"⏰ Run timed out after {max_wait_time} seconds")
    return None


def display_results(run_result: Dict[str, Any]):
    """Display the results of a completed run."""
    if not run_result:
        print("❌ No results to display")
        return

    print("\n" + "=" * 60)
    print("📊 WORKFLOW EXECUTION RESULTS")
    print("=" * 60)

    print(f"Run ID: {run_result['id']}")
    print(f"Status: {run_result['status']}")
    print(f"Started: {run_result.get('started_at', 'N/A')}")
    print(f"Finished: {run_result.get('finished_at', 'N/A')}")

    metrics = run_result.get("metrics", {})
    if "output" in metrics:
        print("\n📝 OUTPUT MESSAGES:")
        print("-" * 40)
        for i, msg in enumerate(metrics["output"], 1):
            msg_type = msg.get("type", "unknown")
            content = msg.get("content", "")
            print(f"{i}. [{msg_type.upper()}] {content}")

    if "inputs" in metrics:
        print(f"\n📥 INPUT: {metrics['inputs']}")


def main():
    """Main demo function."""
    print("🚀 Starting NeoAgent Workflow Demo")
    print("=" * 50)

    # Test questions
    questions = ["What is the current time and tell me about AI agents?"]

    try:
        # Create agent
        print("\n1️⃣ Creating Demo Agent...")
        agent_id = create_simple_agent()
        if not agent_id:
            return

        # Create workflow
        print("\n2️⃣ Creating Demo Workflow...")
        workflow_id = create_simple_workflow(agent_id)
        if not workflow_id:
            return

        # Run workflow for each question
        for i, question in enumerate(questions, 1):
            print(f"\n3️⃣.{i} Running Workflow: '{question}'")
            print("-" * 50)

            run_id = run_workflow(workflow_id, question)
            if not run_id:
                continue

            # Wait for completion
            result = wait_for_completion(run_id)
            display_results(result)

            print("\n" + "=" * 60)
            input("Press Enter to continue to next question...")

    except KeyboardInterrupt:
        print("\n\n⏹️ Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")

    print("\n🏁 Demo completed!")


if __name__ == "__main__":
    main()
